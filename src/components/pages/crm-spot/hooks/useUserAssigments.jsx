import { useEffect, useState } from 'react';
import moment from 'moment';
import { _getUserAssignmentsByRole } from 'controllers/users-assignments/users-assignments-controller';
import {
  _addSchoolAssignment,
  _updateSchoolAssignment,
} from 'controllers/school-assignments/school-assignments-controller';
import { getAssigmentsByUser } from '../utils/get-assigments-by-user';
import { _removeSuperAdmin } from 'controllers/users/users_controller';

export const useUserAssignments = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const loadData = async () => {
    setLoading(true);
    setError(null);
    try {
      const RoleSpotTeam = 2; // Corresponds to SPOT Team
      const response = await _getUserAssignmentsByRole(RoleSpotTeam);
      const data = await response.json();
      const users = getAssigmentsByUser(data);
      setUsers(users);
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  const addSchoolAssignment = async ({ schoolId, userId, status = 'active', beginAt, endAt }) => {
    setLoading(true);
    setError(null);
    const clientTimezoneOffset = new Date().getTimezoneOffset() / 60;
    const payload = {
      school_id: parseInt(schoolId),
      user_id: userId,
      status,
      begin_at: moment(beginAt).add(clientTimezoneOffset, 'hours').format('YYYY-MM-DD HH:mm'),
      end_at:
        status === 'finished' && endAt
          ? moment(endAt).add(clientTimezoneOffset, 'hours').format('YYYY-MM-DD HH:mm')
          : null,
    };
    try {
      await _addSchoolAssignment(payload);
      await loadData();
    } catch (e) {
      setError(e);
      throw e;
    } finally {
      setLoading(false);
    }
  };

  const deleteSchoolAssignment = async ({ assignmentId, userId, beginAt }) => {
    setLoading(true);
    setError(null);
    const clientTimezoneOffset = new Date().getTimezoneOffset() / 60;
    const payload = {
      status: 'deleted',
      begin_at: moment(beginAt).add(clientTimezoneOffset, 'hours').format('YYYY-MM-DD HH:mm'),
      end_at: null,
    };
    try {
      await _updateSchoolAssignment(assignmentId, userId, payload);
      await loadData();
    } catch (error) {
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const removeSuperAdmin = async (user_id) => {
    setLoading(true);
    try {
      const res = await _removeSuperAdmin(user_id);
      if (res.ok) {
        await loadData();
        return { ok: true };
      }
    } catch (error) {
      console.error('Error removing super admin:', error);
      setError();
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  return { users, loading, error, loadData, addSchoolAssignment, deleteSchoolAssignment, removeSuperAdmin };
};
