import { useState } from 'react';
import { _createCloudTalkAgent, _startCloudTalkCall } from 'controllers/cloud-talk/cloud-talk-controller';

export const useCloudtalk = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const createAgent = async ({ user_id, first_name, last_name, email }) => {
    setLoading(true);
    setError(null);
    try {
      const response = await _createCloudTalkAgent({ user_id, first_name, last_name, email });
      const data = await response.json();
      return data;
    } catch (err) {
      setError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const startCall = async ({ user_id, callee_number }) => {
    setLoading(true);
    setError(null);

    try {
      const response = await _startCloudTalkCall({ user_id, callee_number });
      const data = await response.json();
      return data;
    } catch (error) {
      setError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    createAgent,
    startCall,
  };
};
