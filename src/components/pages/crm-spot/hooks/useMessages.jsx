import { useEffect, useState } from 'react';
import {
  _getMessagesByFilters,
  _sendEmailMessageToLead,
  _sendSmsMessageToLead,
} from 'controllers/messages/messages_controllers';

export const useMessages = ({ leadId, schoolId }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [messages, setMessages] = useState([]);

  const getMessages = async () => {
    setLoading(true);
    try {
      const { messages, pagination } = await _getMessagesByFilters({
        school_id: schoolId,
        lead_id: leadId,
        communication_type: 'Both',
      });
      setMessages(messages);
    } catch (error) {
      setError(error);
    } finally {
      setLoading(false);
    }
  };

  // Post a new message (email or sms) and refresh messages
  const postMessage = async ({ messageType, message, subject }) => {
    setLoading(true);
    try {
      let result;
      if (messageType === 'email') {
        const formattedBody = message.replace(/\r?\n/g, '<br />');
        result = await _sendEmailMessageToLead(schoolId, leadId, subject, formattedBody);
      } else {
        result = await _sendSmsMessageToLead(schoolId, leadId, message);
      }
      // Refresh messages after sending
      if (result && result.success) {
        setMessages((prev) => [
          ...prev,
          {
            communication_type: messageType,
            created_at: new Date(),
            body: message,
          },
        ]);
        await getMessages();
        return { success: true, data: result };
      }
      return { success: false, error: 'Message failed' };
    } catch (error) {
      return { success: false, error };
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (leadId) {
      getMessages();
    }
  }, [leadId]);

  return { messages, loading, error, postMessage };
};
