import { useEffect, useState } from 'react';
import { _getUserProfileById, _getUserRoles, _updateUserStatus } from 'controllers/users/users_controller';
import { parseUserStatus } from '../utils/parseUserStatus';
import { setUser } from 'redux/spot/spot-actions';

export const useUserStatus = (userId) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const [data, setData] = useState([]);

  // function to get user Data with _getUserProfileById and _getUserRoles
  const getUserData = async () => {
    setLoading(true);
    setError(null);
    try {
      const [profileResponse, rolesResponse] = await Promise.all([_getUserProfileById(userId), _getUserRoles(userId)]);
      const profileData = await profileResponse.json();
      const rolesData = await rolesResponse.json();
      // Merge roles into profileData
      const roleNames = Array.isArray(rolesData)
        ? rolesData.map(({ role_id, name }) => ({
            role_id,
            name,
          }))
        : [];

      const adminRoleId = 3;
      const isSPOTadmin = rolesData.some(({ role_id }) => role_id === adminRoleId);

      setData({ ...profileData, roles: roleNames || [] });

      // update redux store
      const { userStatus, tag } = parseUserStatus(profileData.status);
      setUser({ status: userStatus, tag, role: roleNames || [], isSPOTadmin, cloudtalkId: profileData.cloudtalk_id });
    } catch (err) {
      setError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateUserStatus = async (userId, status, ownProfile = true) => {
    setLoading(true);
    setError(null);
    try {
      const response = await _updateUserStatus(userId, status);
      await response.json();
      // refetch call
      if (ownProfile) {
        await getUserData();
      }
    } catch (err) {
      setError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (userId) {
      getUserData();
    }
  }, []);

  return {
    status: data?.status,
    role: data?.role,
    updateUserStatus,
    loading,
    error,
  };
};
