import { useEffect, useState } from 'react';
import { Menu, Typo<PERSON>, Row, Col } from 'antd';
import { HomeOutlined, SettingOutlined, TeamOutlined } from '@ant-design/icons';
import { Scales16 } from '@carbon/icons-react';
import { SetupBanner } from './components/setup-banner';
import { browserHistory } from 'react-router';
import styles from './management-console.module.scss';
import { SpotLeadsWelcome } from '../../shared-components/spot-lead-welcome';
import { HeaderTitleButtons } from '../../shared-components/header-title-buttons';
import { useSelector } from 'react-redux';

export const ManagementConsole = ({ children }) => {
  // SPOT admin exclusive access
  const { roles, isSPOTadmin } = useSelector((state) => state.spot);

  useEffect(() => {
    if (roles.length > 0 && !isSPOTadmin) {
      browserHistory.push('/admin/dashboard');
    }
  }, [roles]);

  const [section, setSection] = useState('team-management');

  const handleRouteSelection = (key) => {
    setSection(key);
    browserHistory.push(`/admin/management-console/${key}`);
  };

  const items = [
    {
      key: 'team-management',
      label: (
        <p>
          Team Management <br />
          <span>Manage users and permissions</span>
        </p>
      ),
      icon: <TeamOutlined />,
    },
    {
      key: 'queue-configuration',
      label: (
        <p>
          Queue Configuration <br />
          <span>Set up lead distribution rules</span>
        </p>
      ),
      icon: <SettingOutlined />,
    },
    {
      key: 'lead-balance',
      label: (
        <p>
          Lead Balance <br />
          <span>Configure workload distribution</span>
        </p>
      ),
      icon: <Scales16 fill="#00506c" />,
    },
  ];

  return (
    <div>
      <HeaderTitleButtons
        isButtonsShowed
        button1Text={'Leads Management'}
        button1Icon={<SettingOutlined />}
        button1Type="default"
        button1OnClick={() => browserHistory.push('/admin/leads-management')}
        button2Text="Home"
        button2Icon={<HomeOutlined />}
        button2Type="default"
        button2OnClick={() => browserHistory.push('/admin/dashboard')}
      />
      <SpotLeadsWelcome>
        <SpotLeadsWelcome.Header
          title="Management Console"
          icon={<SettingOutlined />}
          description="Configure and manage team settings, queue behavior, and lead distribution. This console allows you to toggle users’ queue status and manage their school assignments from this page."
        />
        <SpotLeadsWelcome.Footer>
          Controlling <span className="font-700 text-white">system settings</span> and operational parameters
        </SpotLeadsWelcome.Footer>
      </SpotLeadsWelcome>
      <Row gutter={[16, 16]} style={{ padding: '20px' }}>
        <Col span={6}>
          <aside className={styles.aside}>
            <Typography.Title level={5}>Settings</Typography.Title>
            <Menu
              className={styles.menu}
              onClick={(e) => handleRouteSelection(e.key)}
              defaultSelectedKeys={[section]}
              mode="inline"
              items={items}
            />
            <SetupBanner />
          </aside>
        </Col>
        <Col span={18}>
          <section>{children}</section>
        </Col>
      </Row>
    </div>
  );
};
