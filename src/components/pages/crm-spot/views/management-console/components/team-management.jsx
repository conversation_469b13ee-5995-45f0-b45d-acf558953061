import { Table, Button, Input, Dropdown, Switch } from 'antd';
import {
  HomeOutlined,
  TeamOutlined,
  UserAddOutlined,
  UserOutlined,
  SearchOutlined,
  MailOutlined,
  MoreOutlined,
  SafetyOutlined,
  EditOutlined,
  StopOutlined,
  CloseCircleOutlined,
} from '@ant-design/icons';
import { useToggle } from 'hooks/useToggle';
import { UserAssignments } from './user-assignments';
import { UserAnalyticsModal } from './user-analytics-modal';
import { AddUserModal } from './add-user-modal';
import { EditUserModal } from './edit-user-modal';
import { useUserAssignments } from 'components/pages/crm-spot/hooks/useUserAssigments';
import { QueueLayout } from 'components/pages/crm-spot/layouts/queue-layout';
import styles from './team-management.module.scss';
import { useState, useEffect } from 'react';
import { parseUserStatus } from 'components/pages/crm-spot/utils/parseUserStatus';
import { _getRoles, _removeSuperAdmin, _updateUserRole } from 'controllers/users/users_controller';
import { useUserStatus } from 'components/pages/crm-spot/hooks/useUserStatus';
import { getProfile } from 'util/auth';
import { useNotification } from 'components/pages/crm-spot/layouts/crm-spot-layout';

export const TeamManagement = () => {
  const {
    users,
    loadData: getUserAssignments,
    loading,
    addSchoolAssignment,
    deleteSchoolAssignment,
    removeSuperAdmin,
  } = useUserAssignments();
  const [assignOpen, toggleAssign] = useToggle(false);
  const [analyticsOpen, toggleAnalytics] = useToggle(false);
  const [addUserOpen, toggleAddUser] = useToggle(false);
  const [editUserOpen, toggleEditUser] = useToggle(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [searchText, setSearchText] = useState('');
  const { roles, loadingRoles, errorRoles } = useRoles();

  const userId = getProfile().sub;

  const { updateUserStatus } = useUserStatus();

  const handleEditUser = (user) => {
    setCurrentUser(user);
    toggleEditUser();
  };

  const onUpdateUser = async (roleId) => {
    try {
      const res = await _updateUserRole(currentUser.user_id, roleId);
      // refresh the user assignments
      if (!res.ok) {
        await getUserAssignments();
      }
      toggleEditUser();
    } catch (error) {
      console.error('Error updating user role:', error);
    }
  };

  const { openNotification } = useNotification();

  const handleRemoveSuperAdmin = async (user) => {
    const res = await removeSuperAdmin(user.user_id);
    if (res.ok) {
      openNotification({
        type: 'success',
        message: 'Account Removed',
        description: `User ${user.user} was removed successfully.`,
      });
    }
  };

  const handleUserStatus = async (userId, currentStatus) => {
    const newStatus = currentStatus === 'active' ? 'deactive' : 'active';
    const tag = currentStatus === 'active' ? 'away' : 'available';
    await updateUserStatus(userId, `${newStatus}|${tag}`, false);
    await getUserAssignments();
  };

  const handleRevokeAdmin = (userId) => {
    // In a real implementation, this would revoke admin access
    console.log(`Revoking admin access for user ${userId}`);
  };

  const columns = [
    {
      title: 'User',
      dataIndex: 'user',
      key: 'user',
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
      render: (user, record) => (
        <div className={styles.userCell}>
          <div className={styles.userAvatar}>
            <UserOutlined />
          </div>
          <div className={styles.userInfo}>
            <div className={styles.userName}>{user}</div>
            <div className={styles.userEmail}>
              <MailOutlined style={{ fontSize: '10px' }} />
              {record.email}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: 'Role',
      dataIndex: 'role_id',
      key: 'role_id',
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
      render: (role_id) => (
        <div className={styles.roleCell}>
          <SafetyOutlined style={{ color: '#00506c' }} />
          {Array.isArray(roles) ? roles.find((role) => role.id === role_id)?.name : 'Unset' || 'Unset'}
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'user_status',
      key: 'user_status',
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
      render: (user_status) => {
        const { userStatus, tag } = parseUserStatus(user_status);
        return (
          <div className={styles.statusCell}>
            <span className={`${styles.statusBadge} ${userStatus === 'active' ? styles.active : styles.inactive}`}>
              {`${userStatus} - ${tag}` || 'Unset'}
            </span>
          </div>
        );
      },
    },
    {
      title: 'Manage Status',
      dataIndex: 'user_status',
      key: 'user_status_switch',
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
      render: (user_status, record) => {
        const { userStatus } = parseUserStatus(user_status);
        return (
          userId !== record.user_id && (
            <div className={styles.switchCell}>
              <Switch
                checked={userStatus === 'active'}
                onChange={() => handleUserStatus(record.user_id, userStatus)}
                disabled={userStatus === 'deactive' || loading}
                className={styles.statusSwitch}
              />
            </div>
          )
        );
      },
    },
    {
      title: 'Assigned Schools',
      dataIndex: 'schools',
      key: 'schools',
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
      render: (schools, record) => (
        <div className={styles.schoolsCell}>
          <Button
            icon={<HomeOutlined />}
            onClick={() => {
              toggleAssign();
              setCurrentUser(record);
            }}
            className={styles.manageButton}>
            Manage Schools
          </Button>
        </div>
      ),
    },
    {
      title: '',
      key: 'actions',
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
      render: (_, record) => {
        const items = [
          {
            key: 'edit-user',
            label: 'Edit User',
            icon: <EditOutlined />,
            onClick: () => handleEditUser(record),
          },
          ...(userId !== record.user_id
            ? [
                {
                  key: 'deactive-superadmin',
                  label: 'Deactivate Account',
                  icon: <CloseCircleOutlined />,
                  danger: true,
                  onClick: () => handleRemoveSuperAdmin(record),
                },
              ]
            : []),
        ];

        return (
          <div className={styles.actionsCell}>
            <Dropdown
              menu={{ items }}
              placement="bottomRight"
              trigger={['click']}
              overlayClassName={styles.actionDropdown}>
              <Button type="text" icon={<MoreOutlined />} className={styles.moreButton} />
            </Dropdown>
          </div>
        );
      },
    },
  ];

  const filteredUsers = users?.filter((user) => {
    if (!searchText) return true;
    const searchLower = searchText.toLowerCase();
    return (
      user.user.toLowerCase().includes(searchLower) ||
      user.email.toLowerCase().includes(searchLower) ||
      user.role.toLowerCase().includes(searchLower)
    );
  });

  return (
    <QueueLayout
      title="Team Management"
      icon={<TeamOutlined />}
      button1Text="Team Analytics"
      button2Text="Add User"
      button1Icon={<UserOutlined />}
      button2Icon={<UserAddOutlined />}
      button1OnClick={toggleAnalytics}
      button2OnClick={toggleAddUser}>
      <div className={styles.searchContainer}>
        <SearchOutlined className={styles.searchIcon} />
        <Input
          placeholder="Search users..."
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          className={styles.searchInput}
        />
      </div>
      <div className={styles.tableContainer}>
        <Table
          dataSource={filteredUsers}
          columns={columns}
          loading={loading}
          pagination={false}
          rowClassName={styles.tableRow}
          className={styles.table}
        />
      </div>

      <UserAnalyticsModal visible={analyticsOpen} onClose={toggleAnalytics} />
      <AddUserModal visible={addUserOpen} onClose={toggleAddUser} />
      <EditUserModal
        visible={editUserOpen}
        onClose={toggleEditUser}
        onUpdateUser={onUpdateUser}
        roles={roles}
        userData={currentUser}
      />

      <UserAssignments
        assignOpen={assignOpen}
        toggleAssign={toggleAssign}
        user={currentUser}
        addSchoolAssignment={addSchoolAssignment}
        deleteSchoolAssignment={deleteSchoolAssignment}
        loading={loading}
      />
    </QueueLayout>
  );
};

export const useRoles = () => {
  const [roles, setRoles] = useState([]);
  const [loadingRoles, setLoadingRoles] = useState(false);
  const [errorRoles, setErrorRoles] = useState(null);

  useEffect(() => {
    const fetchRoles = async () => {
      setLoadingRoles(true);
      setErrorRoles(null);
      try {
        const response = await _getRoles();
        const data = await response.json();
        setRoles(data);
      } catch (err) {
        setErrorRoles(err);
      } finally {
        setLoadingRoles(false);
      }
    };
    fetchRoles();
  }, []);

  return { roles, loadingRoles, errorRoles };
};
