import React, { useState, useEffect, useMemo } from 'react';
import { Modal, Input, Button, Tag } from 'antd';
import { UserOutlined, DownOutlined } from '@ant-design/icons';
import styles from './edit-user-modal.module.scss';
import { useCloudtalk } from 'components/pages/crm-spot/hooks/useCloudtalk';
import { _createAdminRole, _getUserRoles, _removeSpotAdminRole } from 'controllers/users/users_controller';
import { useNotification } from 'components/pages/crm-spot/layouts/crm-spot-layout';

export const EditUserModal = ({ visible, onClose, userData, roles, onUpdateUser }) => {
  const { createAgent } = useCloudtalk();
  const handleCreateAgent = async () => {
    await createAgent({
      user_id: userData.user_id,
      first_name: userData.first_name,
      last_name: userData.last_name,
      email: userData.email,
    })
      .then((data) => {
        console.log('Agent created successfully', data);
      })
      .catch((error) => {
        console.error('Error creating agent:', error);
      });
  };

  const roleOptions = useMemo(() => {
    return (Array.isArray(roles) ? roles : []).map((role) => ({
      value: role.id,
      label: role.name,
    }));
  }, [roles]);

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    newPassword: '',
    role: null,
  });

  const { userRoles, createAdminRole, removeSpotAdminRole, loading } = useUserRoles(userData?.user_id);

  const adminRoleId = 3;
  const isSPOTAdmin = userRoles?.find((role) => Number(role.role_id) === adminRoleId);

  const { openNotification } = useNotification();

  const handleCreateAdminRole = async () => {
    const res = await createAdminRole();
    if (res) {
      openNotification({
        message: 'New SPOT Admin Assigned',
        description: `${userData?.user} now is SPOT Admin`,
      });
    }
  };

  const handleRemoveAdminRole = async () => {
    const res = await removeSpotAdminRole(isSPOTAdmin.id);
    if (res) {
      openNotification({
        message: 'SPOT Admin Removed',
        description: `${userData?.user} is no longer a SPOT Admin`,
      });
    }
  };

  useEffect(() => {
    if (userData) {
      setFormData({
        name: userData.user || '',
        email: userData.email || '',
        newPassword: '',
        role: userData.role_id,
      });
    }
  }, [userData]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleRoleChange = (value) => {
    setFormData((prev) => ({
      ...prev,
      role: value,
    }));
  };

  const handleSubmit = async () => {
    await onUpdateUser(formData.role);
  };

  return (
    <Modal
      title={null}
      open={visible}
      onCancel={onClose}
      width={500}
      centered
      closeIcon={<span className={styles.closeIcon}>&times;</span>}
      footer={null}>
      <div className={styles.modalHeader}>
        <UserOutlined className={styles.userIcon} />
        <div className={styles.modalTitle}>Edit User Details</div>
      </div>

      <div className={styles.formGroup}>
        <label className={styles.formLabel}>Name</label>
        <Input
          className={styles.formInput}
          placeholder="Enter full name"
          name="name"
          value={formData.name}
          onChange={handleChange}
          disabled
        />
      </div>

      <div className={styles.formGroup}>
        <label className={styles.formLabel}>Email</label>
        <Input
          className={styles.formInput}
          placeholder="Enter email"
          name="email"
          type="email"
          value={formData.email}
          onChange={handleChange}
          disabled
        />
      </div>

      {/* <div className={styles.formGroup}>
        <label className={styles.formLabel}>New Password</label>
        <Input.Password
          className={classNames(styles.formInput, styles.formInputPassword)}
          placeholder="Enter new password"
          name="newPassword"
          value={formData.newPassword}
          onChange={handleChange}
        />
      </div> */}

      <div className={styles.formGroup}>
        <label className={styles.formLabel}>Roles</label>
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          {userRoles?.length > 0 ? (
            userRoles?.map((role) => (
              <Tag key={role.id} color={role.role_id === adminRoleId ? 'green' : 'geekblue'} style={{ marginRight: 8 }}>
                {role.name}
              </Tag>
            ))
          ) : (
            <span>No Roles Set</span>
          )}
        </div>
        <Button
          size="small"
          style={{ marginTop: 8 }}
          onClick={isSPOTAdmin ? handleRemoveAdminRole : handleCreateAdminRole}
          loading={loading}>
          {isSPOTAdmin ? 'Remove as SPOT Admin' : 'Assign as SPOT Admin'}
        </Button>
      </div>

      <div className={styles.formGroup}>
        <span className={styles.formLabel}>Cloud Talk ID:</span>
        {userData?.cloudtalk_id ? (
          <span>{userData?.cloudtalk_id}</span>
        ) : (
          <Button size="small" onClick={handleCreateAgent}>
            Generate Cloud Talk Agent
          </Button>
        )}
      </div>
    </Modal>
  );
};

const useUserRoles = (userId) => {
  const [userRoles, setUserRoles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchUserRoles = async () => {
    if (userId) {
      setLoading(true);
      setError(null);
      try {
        const res = await _getUserRoles(userId);
        const data = await res.json();
        setUserRoles(data);
      } catch (err) {
        setError(err);
        console.error('Failed to fetch user roles:', err);
      } finally {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    if (userId) {
      fetchUserRoles();
    }
  }, [userId]);

  const createAdminRole = async () => {
    setLoading(true);
    setError(null);
    try {
      await _createAdminRole(userId);
      await fetchUserRoles();
      return true;
    } catch (error) {
      setError(error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const removeSpotAdminRole = async (roleId) => {
    setLoading(true);
    setError(null);
    try {
      await _removeSpotAdminRole(userId, roleId);
      await fetchUserRoles();
      return true;
    } catch (error) {
      setError(error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  return { userRoles, createAdminRole, removeSpotAdminRole, loading, error };
};
