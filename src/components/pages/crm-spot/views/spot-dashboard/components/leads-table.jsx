import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { Table, Checkbox, Flex, <PERSON><PERSON>, Di<PERSON><PERSON>, Drawer, Tooltip } from 'antd';
import {
  PhoneOutlined,
  MessageOutlined,
  ArrowRightOutlined,
  CheckSquareOutlined,
  CheckOutlined,
  HistoryOutlined,
} from '@ant-design/icons';
import {
  openTaskLead,
  openSchoolDetails,
  closeSchoolDetails,
  openUpdateLead,
  setSelectedLeads,
  handleSelectAllLeads,
  clearSelectedLeads,
  setSelectedLead,
  setGlobalValue,
} from 'redux/spot/spot-actions';
import { SchoolDetails } from '.';
import { StatusTag } from 'components/pages/crm-spot/shared-components/status-tag';
import { SchoolName } from 'components/pages/crm-spot/shared-components';
import { useToggle } from 'hooks/useToggle';
import { _sendEmailMessageToLead, _sendSmsMessageToLead } from 'controllers/messages/messages_controllers';
import { MessageModal } from './message-modal';
import { CompareSchoolsModal } from './compare-schools-modal';
import { SubmitApplicationModal } from './submit-application-modal';
import { LeadHistoryModal } from './lead-history-modal';
import { format, parseISO } from 'date-fns';
import { Scales16 } from '@carbon/icons-react';
import { useSchoolsMatch } from 'components/pages/crm-spot/hooks/useSchoolsMatch';
import { useCloudtalk } from 'components/pages/crm-spot/hooks/useCloudtalk';
import styles from './leads-table.module.scss';

export const LeadsTableContent = ({ leads, loading, userId }) => {
  const { isAssignedMode, isSchoolDetailsOpen, selectedLead, selectedLeads, cloudtalkId } = useSelector(
    (state) => state.spot
  );

  const { startCall, loading: loadingCall } = useCloudtalk();

  const handleSelectAll = () => {
    if (selectedLeads.length === leads.length) {
      handleSelectAllLeads([]);
    } else {
      handleSelectAllLeads(leads.map((lead) => lead.id));
    }
  };

  const [openMessage, toggleMessage] = useToggle(false);
  const [isHistoryModalOpen, toggleHistoryModalOpen] = useToggle(false);

  const columns = [
    {
      title: (
        <Checkbox onChange={handleSelectAll} checked={selectedLeads.length === leads.length && leads.length > 0} />
      ),
      dataIndex: 'id',
      key: 'id',
      width: 50,
      render: (id, lead) => (
        <Checkbox checked={selectedLeads.some((item) => item === id)} onChange={() => setSelectedLeads(id)} />
      ),
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
    },
    ...(isAssignedMode
      ? [
          {
            title: 'School',
            dataIndex: 'school_name',
            key: 'school_name',
            width: 250,
            onHeaderCell: () => ({
              style: { backgroundColor: '#00506c', color: '#ffffff' },
            }),
            render: (school_name, record) => (
              <span>
                {record.school_id} - {school_name}
              </span>
            ),
          },
        ]
      : []),
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 150,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
      render: (status) => <StatusTag status={status} />,
    },
    {
      title: 'Lead ID',
      dataIndex: 'id',
      key: 'id',
      sorter: true,
      width: 75,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
    },
    {
      title: 'Date Added',
      dataIndex: 'created_at',
      key: 'created_at',
      sorter: true,
      width: 125,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
      render: (created_at) => {
        if (!created_at) return '';
        let dateObj;
        try {
          dateObj = typeof created_at === 'string' ? parseISO(created_at) : new Date(created_at);
        } catch {
          dateObj = new Date(created_at);
        }
        return format(dateObj, 'MM-dd-yyyy');
      },
    },
    {
      title: 'Name',
      key: 'name',
      width: 150,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
      render: (_, record) => `${record.parent_first_name} ${record.parent_last_name}`,
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      width: 200,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
    },
    {
      title: 'Student',
      key: 'student',
      width: 150,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
      render: (_, record) => `${record.child_first_name} ${record.child_last_name}`,
    },
    {
      title: 'Phone',
      dataIndex: 'phone',
      key: 'phone',
      width: 125,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
    },
    {
      title: 'Grade',
      dataIndex: 'grade',
      key: 'grade',
      width: 75,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
    },
    {
      title: 'Lead Source',
      dataIndex: 'lead_source',
      key: 'lead_source',
      width: 150,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
    },
    {
      title: 'Schools Actions',
      dataIndex: 'id',
      key: 'actions',
      fixed: 'right',
      width: 350,
      onHeaderCell: () => ({
        style: { backgroundColor: '#00506c', color: '#ffffff' },
      }),
      render: (id, record) => (
        <LeadActions
          onClick={(e) => e.stopPropagation()}
          userId={userId}
          lead={record}
          cloudtalkId={cloudtalkId}
          startCall={startCall}
          loadingCall={loadingCall}
          isAssignedMode={isAssignedMode}
          onOpenDrawer={() => openSchoolDetails(id)}
          toggleMessage={toggleMessage}
          toggleHistoryModalOpen={toggleHistoryModalOpen}
        />
      ),
    },
  ];

  const classNames = {
    header: styles.drawerHeader,
    body: styles.drawerBody,
  };

  return (
    <>
      {selectedLeads.length > 0 && (
        <SelectionEdit
          selectedLeads={selectedLeads.length}
          openUpdateLead={openUpdateLead}
          clearSelectedLeads={clearSelectedLeads}
        />
      )}
      <Table
        className={styles.leadsTable}
        rowKey="id"
        dataSource={leads}
        columns={columns}
        pagination={false}
        rowClassName={(record) => (record.id === selectedLead?.id ? `${styles.row} ${styles.selectedRow}` : styles.row)}
        scroll={{ x: 1500 }}
        loading={loading}
        onRow={(lead) => ({
          onClick: () => {
            setSelectedLead(lead);
            setGlobalValue('isLeadEdition', true);
          },
        })}
      />

      <MessageModal open={openMessage} toggleMessage={toggleMessage} />

      <Drawer
        classNames={classNames}
        placement="right"
        onClose={closeSchoolDetails}
        open={isSchoolDetailsOpen}
        width={599}>
        <SchoolDetails closeSchoolDetails={closeSchoolDetails} />
      </Drawer>

      <LeadHistoryModal open={isHistoryModalOpen} onClose={toggleHistoryModalOpen} />
    </>
  );
};

const LeadActions = ({
  userId,
  isAssignedMode,
  onOpenDrawer,
  lead,
  toggleMessage,
  cloudtalkId,
  startCall,
  loadingCall,
  toggleHistoryModalOpen,
  ...props
}) => {
  const [isCompareModalOpen, setIsCompareModalOpen] = useState(false);
  const [isSubmitApplicationModalOpen, setIsSubmitApplicationModalOpen] = useState(false);
  // const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);

  const { schools, loading, error } = useSchoolsMatch(lead.scholamatch_url) || {};

  const handleCompareSchools = () => {
    setIsCompareModalOpen(true);
  };

  const handleCloseCompareModal = () => {
    setIsCompareModalOpen(false);
  };

  const handleOpenSubmitApplicationModal = () => {
    setIsSubmitApplicationModalOpen(true);
  };

  const handleCloseSubmitApplicationModal = () => {
    setIsSubmitApplicationModalOpen(false);
  };

  const actions = [
    {
      Icon: PhoneOutlined,
      color: '#2563eb',
      onClick: () => startCall({ user_id: userId, callee_number: lead.phone }),
      tooltip: `${cloudtalkId ? `${loadingCall ? 'Calling...' : 'Call'}` : 'Contact Admin to set up CloudTalk'}`,
      disabled: !cloudtalkId,
      loading: loadingCall,
    },
    {
      Icon: MessageOutlined,
      color: '#9333ea',
      onClick: () => {
        toggleMessage();
        setSelectedLead(lead);
      },
      tooltip: 'Conversations',
    },
    {
      Icon: CheckSquareOutlined,
      color: '#f97316',
      onClick: () => {
        openTaskLead();
        setSelectedLead(lead);
      },
      tooltip: 'Task',
    },
    {
      Icon: HistoryOutlined,
      color: '#2563eb',
      onClick: () => {
        setSelectedLead(lead);
        toggleHistoryModalOpen();
      },
      tooltip: 'History',
    },
    {
      Icon: ArrowRightOutlined,
      color: '#6466f1',
      onClick: () => {
        window.open('https://scholamatch.com/scholaMatch/basic-info', '_blank', 'noopener,noreferrer');
      },
      tooltip: 'ScholaMatch',
    },
    {
      Icon: CheckOutlined,
      color: '#6466f1',
      onClick: () => {
        openUpdateLead();
        setSelectedLead(lead);
      },
      tooltip: 'Update Stage',
    },
  ];

  return (
    <div className={styles.cellStyle} {...props}>
      {!isAssignedMode && (
        <>
          {lead.scholamatch_url ? (
            <>
              {loading ? (
                <div className="spacing-mb-8">Loading school matches...</div>
              ) : error ? (
                <div className="spacing-mb-8" style={{ color: 'red' }}>
                  Error loading schools
                </div>
              ) : schools?.length > 0 ? (
                <>
                  {schools.map((school) => (
                    <div key={school.school_id} className="spacing-mb-8">
                      <SchoolName name={school.school_name} matchLevel={school.position} />
                    </div>
                  ))}
                  <Button icon={<Scales16 />} className="spacing-mb-8" onClick={handleCompareSchools}>
                    Compare Schools
                  </Button>
                  <CompareSchoolsModal
                    open={isCompareModalOpen}
                    onClose={handleCloseCompareModal}
                    schools={schools || []}
                  />
                </>
              ) : (
                <div className="spacing-mb-8">No school matches found</div>
              )}
            </>
          ) : (
            <div className={styles.incomplete}>
              <p>Did Not Complete ScholaMatch</p>
            </div>
          )}
          <Divider className="spacing-my-8" />
        </>
      )}
      <p className="text-dark-blue-75 text-uppercase spacing-mb-8">Quick Actions</p>
      <div className="box spacing-px-8 spacing-m-0">
        <Flex gap="small" justify="center" align="center">
          {actions.map((action, index) => (
            <ActionButton
              key={index}
              Icon={action.Icon}
              color={action.color}
              onClick={action.onClick}
              tooltip={action.tooltip}
              disabled={action.disabled}
              loading={action.loading}
            />
          ))}
        </Flex>
        {!isAssignedMode && schools?.length > 0 && (
          <>
            <Divider className="spacing-my-8" />
            <Button
              type="primary"
              className="spacing-mt-0 w-100"
              icon={<CheckOutlined />}
              onClick={handleOpenSubmitApplicationModal}>
              Submit Application
            </Button>
            <SubmitApplicationModal open={isSubmitApplicationModalOpen} onClose={handleCloseSubmitApplicationModal} />
          </>
        )}
      </div>
    </div>
  );
};

const ActionButton = ({ Icon, onClick, color, tooltip, disabled, loading }) => {
  return (
    <div>
      <Tooltip title={tooltip}>
        <Button
          disabled={disabled}
          type="button"
          onClick={onClick}
          className={styles.buttonStyle}
          style={{ color: color || '#000' }}
          icon={<Icon />}
          loading={loading}
        />
      </Tooltip>
    </div>
  );
};

const SelectionEdit = ({ selectedLeads, openUpdateLead, clearSelectedLeads }) => {
  return (
    <div className={styles.selectionContainer}>
      <div className={styles.selectionText}>{selectedLeads} leads selected</div>
      <div className={styles.selectionActions}>
        <Button type="default" onClick={clearSelectedLeads} className={styles.clearButton}>
          Clear Selection
        </Button>
        <Button type="primary" onClick={openUpdateLead} className={styles.updateButton}>
          Update Stage
        </Button>
      </div>
    </div>
  );
};
