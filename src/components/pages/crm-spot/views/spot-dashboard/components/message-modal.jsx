import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { Modal, Button, Input, Segmented, Tag, Empty, Skeleton, Flex } from 'antd';
import { useNotification } from 'components/pages/crm-spot/layouts/crm-spot-layout';
import { _sendEmailMessageToLead, _sendSmsMessageToLead } from 'controllers/messages/messages_controllers';
import { formatDate } from 'components/pages/crm-spot/utils/format-date';
import { useMessages } from 'components/pages/crm-spot/hooks/useMessages';
import { MailOutlined, MessageOutlined } from '@ant-design/icons';
import HtmlParser from 'html-react-parser';
import styles from './message-modal.module.scss';

export const MessageModal = ({ open, toggleMessage }) => {
  const { selectedLead } = useSelector((state) => state.spot);

  const {
    messages: rawMessages,
    postMessage,
    loading,
  } = useMessages({
    leadId: selectedLead?.id,
    schoolId: selectedLead?.school_id,
  });

  const [messages, setMessages] = useState([]);
  const [messageType, setMessageType] = useState('sms');
  const [viewSeparate, setViewSeparate] = useState(true);

  useEffect(() => {
    if (!rawMessages) return;
    if (!viewSeparate) {
      setMessages(rawMessages);
    } else {
      setMessages(
        rawMessages.filter(
          (msg) =>
            (messageType === 'sms' && msg.communication_type === 'Sms') ||
            (messageType === 'email' && msg.communication_type === 'Email')
        )
      );
    }
  }, [rawMessages, messageType, viewSeparate]);

  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const handleMessageChange = (value) => setMessage(value);
  const handleSubjectChange = (value) => setSubject(value);

  const { openNotification } = useNotification();

  // Mock conversation data
  /* const mockConversations = [
    {
      id: 1,
      type: 'text',
      sender: 'Schola Team',
      timestamp: 'May 20, 2024 at 10:15 AM',
      content: 'Just a reminder that we have a tour scheduled for tomorrow at 2pm.',
      direction: 'sent',
      attachments: ['brochure.pdf'],
    },
    {
      id: 2,
      type: 'text',
      sender: `Test Lead ${selectedLead?.id || '12370'}`,
      timestamp: 'May 20, 2024 at 10:30 AM',
      content: "Thanks for the reminder. I'll be there.",
      direction: 'received',
    },
    {
      id: 3,
      type: 'text',
      sender: 'Schola Team',
      timestamp: 'May 22, 2024 at 9:30 AM',
      content: 'Would you be available for a quick call tomorrow to discuss the STEM program in more detail?',
      direction: 'sent',
    },
    {
      id: 4,
      type: 'email',
      sender: 'Schola Team',
      timestamp: 'May 15, 2024 at 2:30 PM',
      content: 'Hello, I wanted to follow up on your interest in our schools.',
      subject: 'Follow-up on your interest',
      direction: 'sent',
    },
    {
      id: 5,
      type: 'email',
      sender: `Test Lead ${selectedLead?.id || '12370'}`,
      timestamp: 'May 15, 2024 at 3:45 PM',
      content: "Thank you for your help. I'm interested in scheduling a tour.",
      subject: 'Re: Follow-up on your interest',
      direction: 'received',
    },
    {
      id: 6,
      type: 'email',
      sender: 'Schola Team',
      timestamp: 'May 21, 2024 at 9:00 AM',
      content: '',
      subject: 'School Programs Information',
      direction: 'sent',
    },
  ]; */

  // Handle attachment upload
  const handleAttachmentUpload = (event, type) => {
    const files = Array.from(event.target.files);
    if (type === 'sms') {
      setSmsAttachments((prev) => [...prev, ...files]);
    } else {
      setEmailAttachments((prev) => [...prev, ...files]);
    }
  };

  // Remove attachment
  const removeAttachment = (index, type) => {
    if (type === 'sms') {
      setSmsAttachments((prev) => prev.filter((_, i) => i !== index));
    } else {
      setEmailAttachments((prev) => prev.filter((_, i) => i !== index));
    }
  };

  // Handle send message
  const sendMessageToLead = async () => {
    try {
      const response = await postMessage({
        messageType,
        message,
        subject,
      });

      if (response.success) {
        setSubject('');
        setMessage('');
        openNotification({
          type: 'success',
          message: `${messageType === 'sms' ? 'Text' : 'Email'} sent successfully`,
          description: `Message sent to lead ${selectedLead.id}`,
        });
        return;
      }

      openNotification({
        type: 'warning',
        message: `${messageType === 'sms' ? 'Text' : 'Email'} failed to send`,
        description: `Message failed`,
      });
    } catch (error) {
      openNotification({
        type: 'warning',
        message: `Message failed to send`,
        description: `An error occurred while sending the message`,
      });
    }
  };

  return (
    <Modal
      open={open}
      onCancel={toggleMessage}
      width={700}
      centered={false}
      title={`Conversations with lead: ${selectedLead?.id}`}
      footer={
        viewSeparate
          ? [
              <Button onClick={toggleMessage}>Cancel</Button>,
              <Button type="primary" onClick={sendMessageToLead}>
                Send
              </Button>,
            ]
          : []
      }>
      <Flex justify="flex-end" align="center" gap={4} className="spacing-mb-8">
        View mode:
        <button
          value={viewSeparate}
          className={`${styles.button} ${viewSeparate ? styles.separate : styles.combined}`}
          onClick={() => setViewSeparate((prev) => !prev)}>
          {viewSeparate ? 'Separate' : 'Combined'}
        </button>
      </Flex>
      {viewSeparate && (
        <Segmented options={['SMS', 'Email']} onChange={(value) => setMessageType(value.toLowerCase())} block />
      )}
      <div style={{ margin: '1rem 0' }}>
        <span className="font-600">
          Sending as:{' '}
          <Tag style={{ color: '#008085' }} color="#0080851a">
            Schola Team
          </Tag>
        </span>
        <span className="font-600">
          School ID:{' '}
          <Tag style={{ color: '#008085' }} color="#0080851a">
            {selectedLead?.school_id}
          </Tag>
        </span>
      </div>
      <div>
        {loading && (
          <div style={{ margin: '1rem 0' }}>
            <Skeleton active paragraph={{ rows: 4 }} />
          </div>
        )}
        {!loading && messages.length === 0 ? (
          <Empty description="No previous messages" />
        ) : (
          <div>
            {!viewSeparate && (
              <p className={styles.combinedTitle}>Showing combined email and text conversation history</p>
            )}
            <ul className={styles.list}>
              {messages.map((message) => (
                <li
                  key={message.id}
                  className={`${styles.messageItem} ${message.message_direction !== 'Outbound' && styles.inbound}`}>
                  <Flex className={`${styles.head} spacing-pb-5`} justify="space-between" align="center" gap={4}>
                    <Flex align="center" gap={4}>
                      <div className={styles.messageIcon}>
                        {message.communication_type === 'Sms' ? <MessageOutlined /> : <MailOutlined />}
                      </div>
                      <h5 className="font-600">Schola Team</h5>
                      <p className={styles.date}>{formatDate(message.created_at)}</p>
                    </Flex>
                    <div className={styles.statusBadge}>
                      {message.message_direction === 'Outbound' ? 'Sent' : 'Received'}
                    </div>
                  </Flex>
                  {message.communication_type === 'Email' ? (
                    <>
                      <p className={`${styles.subject} spacing-mt-8`}>
                        <span className="font-600  spacing-mr-8">Subject:</span>
                        {message.subject}
                      </p>
                      <div className="font-14">{HtmlParser(message.body)}</div>
                    </>
                  ) : (
                    <p className={`${styles.body} font-14`}>{message.body}</p>
                  )}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
      {!viewSeparate ? (
        <p className={styles.combinedLabel}>Switch to separate view to send messages</p>
      ) : (
        <>
          {messageType === 'email' && (
            <div style={{ marginBottom: '1rem' }}>
              <label htmlFor="subject" style={{ display: 'block', marginBottom: 4 }}>
                Subject
              </label>
              <Input
                id="subject"
                type="text"
                placeholder="Subject"
                value={subject}
                onChange={(e) => handleSubjectChange(e.target.value)}
              />
            </div>
          )}
          <div style={{ marginBottom: '1rem' }}>
            <label htmlFor="message" style={{ display: 'block', marginBottom: 4 }}>
              {messageType === 'email' ? 'Email Message' : 'SMS Message'}
            </label>
            <textarea
              id="message"
              value={message}
              onChange={(e) => handleMessageChange(e.target.value)}
              style={{ minHeight: '120px', width: '100%' }}
            />
          </div>
        </>
      )}
    </Modal>
  );
};
