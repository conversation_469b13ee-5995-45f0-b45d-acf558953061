.list {
  margin: 1rem 0;
  padding: 0.5rem;
  border: 1px solid #dbeafe;
  border-radius: 0.5rem;
  display: flex;
  flex-direction: column-reverse;
  gap: 1rem;
  max-height: 300px;
  overflow-y: auto;
}

.messageItem {
  background-color: #eff6ff66;
  border: 1px solid #dbeafe;
  box-shadow: 0 0 #0000, 0 0 #0000, 0 1px 2px 0 rgb(0 0 0 / 0.05);
  border-radius: 0.5rem;
  padding: 16px;
  width: 75%;
  align-self: end;
}

.inbound {
  background-color: #f9fafb;
  align-self: initial;
}

.head {
  /* display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 0.5rem;
  gap: 0.5rem; */
  border-bottom: 1px solid #e2e8f0;

  & h5 {
    font-weight: 500;
  }
}

.body {
  margin-top: 0.5rem;
}

.date {
  font-size: 0.75rem;
  color: #64748b;
}

.button {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  display: block;
  justify-self: end;

  &:focus {
    outline: none;
  }
}

.separate {
  background-color: rgb(241, 240, 251);
  border-color: 1px solid rgb(229, 231, 235);
}

.combined {
  background-color: rgb(211, 228, 253);
  border-color: 1px solid rgb(147, 197, 253);
}

.combinedTitle {
  text-align: center;
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
}
.combinedLabel {
  text-align: center;
  padding: 1rem;
  background: #f1f5f94d;
  border-radius: 6px;
  margin-top: 1rem;
  color: #64748b;
  font-size: 1rem;
  border-radius: 0.5rem;
}
.messageIcon {
  background: #dbeafe;
  color: #3b82f6;
  padding: 0.1rem 0.25rem;
  border-radius: 100%;
}
.statusBadge {
  background: #dbeafe;
  color: #020817;
  font-size: 11px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}
.subject {
  font-size: 14px;
}

// overwrited styles:

// .messageModal {
//   .ant-modal-content {
//     padding: 0;
//   }

//   .ant-modal-header {
//     padding: 1rem 1.5rem;
//     border-bottom: 1px solid #f0f0f0;
//   }

//   .ant-modal-body {
//     padding: 0;
//   }
// }

// .viewModeContainer {
//   display: flex;
//   justify-content: flex-end;
//   align-items: center;
//   gap: 0.5rem;
//   padding: 1rem 1.5rem 0;

//   .viewModeLabel {
//     font-size: 14px;
//     color: #666;
//   }

//   .viewModeButton {
//     border: 1px solid #d9d9d9;
//     background: #f5f5f5;
//     color: #666;

//     &:hover {
//       border-color: #40a9ff;
//       color: #40a9ff;
//     }
//   }
// }

// .tabContainer {
//   padding: 1rem 0;

//   .segmentedTabs {
//     background: #f5f5f5;
//     border-radius: 8px;
//     padding: 4px;

//     .ant-segmented-item {
//       border-radius: 6px;

//       &.ant-segmented-item-selected {
//         background: white;
//         box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
//       }
//     }
//   }

//   .tabOption {
//     display: flex;
//     align-items: center;
//     gap: 0.5rem;
//     padding: 0.5rem 1rem;

//     span {
//       font-weight: 500;
//     }
//   }
// }

// .senderInfo {
//   display: flex;
//   align-items: center;
//   gap: 0.75rem;
//   padding: 1rem 1.5rem;
//   background: #fafafa;
//   border-bottom: 1px solid #f0f0f0;

//   .sendingAs {
//     font-weight: 500;
//   }

//   .senderBadge {
//     background: #0080851a;
//     border: 1px solid #00808533;
//     border-radius: 4px;
//     padding: 2px 8px;
//     font-size: 12px;
//     :global {
//       .ant-badge-status-text {
//         color: #008085 !important;
//       }
//     }
//   }

//   .scholaId {
//     font-weight: 500;
//     color: #008085 !important;
//   }

//   .scholaIdLink {
//     color: #008085 !important;
//     text-decoration: none;

//     &:hover {
//       text-decoration: underline;
//     }
//   }
// }

// .conversationHistory {
//   max-height: 400px;
//   overflow-y: auto;
//   padding: 1rem 1.5rem;
//   background: #fafafa;

//   .messageWrapper {
//     display: flex;
//     width: 100%;
//     margin-bottom: 1rem;

//     &.sent {
//       justify-content: flex-end;

//       .messageItem {
//         max-width: 70%;
//         background: #eff6ff66;
//         border: 1px solid #dbeafe;
//         border-radius: 12px;

//
//       }
//     }

//     &.received {
//       justify-content: flex-start;

//       .messageItem {
//         max-width: 70%;
//         background: #f9fafb;
//         border: 1px solid #e5e7eb;
//         border-radius: 12px;

//         .messageIcon {
//           background: #f0f0f0;
//           color: #f97316;
//         }

//         .statusBadge {
//           background: #f1f5f9;
//           color: #0f172a;
//           font-size: 11px;
//           padding: 2px 8px;
//           border-radius: 12px;
//           font-weight: 500;
//         }
//       }
//     }
//   }

//   .messageItem {
//     padding: 1rem;

//     .messageHeader {
//       display: flex;
//       align-items: center;
//       gap: 0.75rem;
//       margin-bottom: 0.75rem;

//       .messageIcon {
//         display: flex;
//         align-items: center;
//         justify-content: center;
//         width: 24px;
//         height: 24px;
//         border-radius: 50%;
//         flex-shrink: 0;
//       }

//       .messageInfo {
//         flex: 1;

//         .senderName {
//           font-weight: 600;
//           color: #333;
//           display: block;
//           font-size: 14px;
//         }

//         .timestamp {
//           font-size: 12px;
//           color: #999;
//           display: block;
//           margin-top: 2px;
//         }
//       }

//       .statusBadge {
//         flex-shrink: 0;
//       }
//     }

//

//     .messageContent {
//       color: #333;
//       line-height: 1.5;
//       margin-bottom: 0.5rem;
//       font-size: 14px;
//     }

//     .attachments {
//       display: flex;
//       align-items: center;
//       gap: 0.5rem;
//       font-size: 12px;
//       color: #666;
//       margin-top: 0.5rem;

//       .attachment {
//         display: flex;
//         align-items: center;
//         gap: 0.25rem;
//         background: rgba(0, 0, 0, 0.05);
//         padding: 4px 8px;
//         border-radius: 8px;
//         font-size: 11px;
//       }
//     }
//   }
// }

// .messageInputMessage {
//   padding: 1rem 0;
//   border-top: 1px solid #f0f0f0;
//   background: white;

//   .subjectInput {
//     margin-bottom: 1rem;
//   }

//   .textareaContainer {
//     position: relative;
//     margin-bottom: 1rem;

//     .messageTextarea {
//       width: 100%;
//       min-height: 100px;
//       padding: 0.75rem;
//       border: 1px solid #d9d9d9;
//       border-radius: 6px;
//       resize: vertical;
//       font-family: inherit;
//       font-size: 14px;
//       line-height: 1.5;

//       &:focus {
//         outline: none;
//         border-color: #40a9ff;
//         box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
//       }

//       &::placeholder {
//         color: #bfbfbf;
//       }
//     }

//     .emojiButton {
//       position: absolute;
//       bottom: 0.75rem;
//       right: 0.75rem;
//     }
//   }

//   .attachmentsList {
//     display: flex;
//     flex-wrap: wrap;
//     gap: 0.5rem;
//     margin-bottom: 1rem;

//     .attachmentItem {
//       display: flex;
//       align-items: center;
//       gap: 0.5rem;
//       background: #f0f0f0;
//       padding: 0.5rem;
//       border-radius: 4px;
//       font-size: 12px;

//       span {
//         max-width: 120px;
//         overflow: hidden;
//         text-overflow: ellipsis;
//         white-space: nowrap;
//       }
//     }
//   }

//   .actionButtons {
//     display: flex;
//     justify-content: space-between;
//     align-items: center;

//     /*     .leftActions {
//       .attachButton {
//         &:hover {
//           color: #1890ff;
//         }
//       }
//     } */

//     .sendButton {
//       background: #00a693;
//       border-color: #00a693;

//       &:hover {
//         background: #008a7a;
//         border-color: #008a7a;
//       }

//       &:disabled {
//         background: #f5f5f5;
//         border-color: #d9d9d9;
//         color: #bfbfbf;
//       }
//     }
//   }
// }

// // Responsive design
// @media (max-width: 768px) {
//   .messageModal {
//     .ant-modal {
//       margin: 0;
//       max-width: 100vw;
//       top: 0;
//     }

//     .ant-modal-content {
//       border-radius: 0;
//     }
//   }

//   .conversationHistory {
//     max-height: 300px;

//     .messageWrapper {
//       &.sent .messageItem,
//       &.received .messageItem {
//         max-width: 85%;
//       }
//     }
//   }

//   .senderInfo {
//     flex-wrap: wrap;
//     gap: 0.5rem;
//   }

//   .actionButtons {
//     flex-direction: column;
//     gap: 1rem;
//     align-items: stretch;

//     .sendButton {
//       width: 100%;
//     }
//   }
// }

// // Combined mode styles
// .combinedModeMessage {
//   text-align: center;
//   padding: 1rem;
//   background: #f1f5f94d;
//   border-radius: 6px;
//   margin-top: 1rem;
//   color: #64748b;
//   font-size: 1rem;
//   border-radius: 0.5rem;
// }
