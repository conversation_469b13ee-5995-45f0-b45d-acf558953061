import { useState } from 'react';
import PropTypes from 'prop-types';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Typography } from 'antd';
import { DownOutlined, UpOutlined, BarChartOutlined } from '@ant-design/icons';

import styles from './lead-header.module.scss';
import { AnalyticsSection } from './analytics-section';
import { getDateRange } from '../utils/get-date-range';
import { useTeamAnalytics } from '../hooks/useTeamBalance';

const { Title } = Typography;

/**
 * LeadHeader
 *
 * Component that displays the header of the leads section, including the title,
 * badges with counts of assigned leads, and a button to show/hide analytics.
 *
 * @param {Object} props - Component properties
 * @param {Array<Object>} props.leads - List of leads to display and analyze
 * @param {string} props.leads[].assignedTo - User assigned to the lead
 * @param {string} props.leads[].status - Lead status ('new', 'attempting', 'working', etc.)
 * @param {Array} [props.leads[].schools] - Schools associated with the lead
 */
export const LeadHeader = ({ leads }) => {
  const [isAnalyticsOpen, setIsAnalyticsOpen] = useState(false);

  const { from, to } = getDateRange('month');
  const { analytics, loading: loadingAnalytics } = useTeamAnalytics({
    from,
    to,
  });

  // Calculate assigned leads for working stages (new, attempting, working)
  const assignedToCurrentUser = leads.filter(
    (lead) =>
      lead.assignedTo === 'current-user' &&
      (lead.status === 'new' || lead.status === 'attempting' || lead.status === 'working')
  ).length;

  // Calculate assigned school leads
  const assignedSchoolLeads = leads.filter(
    (lead) => lead.assignedTo === 'current-user' && lead.schools && lead.schools.length > 0
  ).length;

  const toggleAnalytics = () => setIsAnalyticsOpen(!isAnalyticsOpen);

  return (
    <div className={styles.headerContainer}>
      <div className={styles.container}>
        <div className={styles.headerRow} onClick={toggleAnalytics}>
          <div className={styles.leftSection}>
            <Title level={3} className={styles.title}>
              Lead Queue & Analytics
            </Title>
            <div className={styles.badgeContainer}>
              <span className={styles.badge}>{assignedToCurrentUser} New Assigned to You</span>
              <span className={styles.badge}>{assignedSchoolLeads} School Leads Assigned to You</span>
            </div>
          </div>
          <div className={styles.buttonContainer}>
            <Button
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                toggleAnalytics();
              }}
              icon={<BarChartOutlined />}>
              Analytics
              {isAnalyticsOpen ? <UpOutlined /> : <DownOutlined />}
            </Button>
          </div>
        </div>
      </div>

      {isAnalyticsOpen && (
        <>
          <Divider className={styles.divider} />
          <AnalyticsSection analytics={analytics} loading={loadingAnalytics} />
          <Divider className={styles.dividerBottom} />
        </>
      )}
    </div>
  );
};

LeadHeader.propTypes = {
  leads: PropTypes.arrayOf(
    PropTypes.shape({
      assignedTo: PropTypes.string.isRequired,
      status: PropTypes.string.isRequired,
      schools: PropTypes.array,
    })
  ).isRequired,
};
