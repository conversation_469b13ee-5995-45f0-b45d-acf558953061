@import '../../../../sass/legacy/color-palette';

.notificationModal {
  :global(.ant-modal-content) {
    border-radius: 0.5rem;
    overflow: hidden;
  }

  :global(.ant-modal-header) {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid $dark-blue-15;
  }

  :global(.ant-modal-body) {
    padding: 0;
  }
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.titleWithBadge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.125rem;
  font-weight: 600;
}

.headerBadge {
  background-color: $primary-blue;
}

.headerActions {
  display: flex;
  align-items: center;
}

.headerButton {
  color: $primary-blue;
  font-size: 0.875rem;
  padding: 0 0.5rem;
  height: auto;

  &:hover {
    color: $primary-500-default-link;
  }
}

.closeButton {
  margin-left: 0.5rem;
  color: $dark-blue-400-subtle-icon-border;

  &:hover {
    color: $dark-blue-700-text-icon;
  }
}

.tabs {
  padding: 0 2rem;
  background-color: #eff6ff80;
  margin-bottom: 0.5rem;

  :global(.ant-tabs-nav) {
    margin-bottom: 0;
  }

  :global(.ant-tabs-tab) {
    padding: 0.75rem 0;
  }

  :global(.ant-tabs-tab-active) {
    font-weight: 600;
  }
}

.tabBadge {
  margin-left: 0.25rem;
  background-color: $dark-blue-300-subtle-border-disabled-text;
}

.allBadge {
  margin-left: 0.25rem;
  sup {
    background-color: #e5e7eb !important;
    color: $dark-blue-100 !important;
  }
}
.leadBadge {
  margin-left: 0.25rem;
  sup {
    background-color: #dbeafe !important;
    color: $dark-blue-100 !important;
  }
}
.taskBadge {
  margin-left: 0.25rem;
  sup {
    background-color: #fef3c7 !important;
    color: #92400e !important;
  }
}
.systemBadge {
  margin-left: 0.25rem;
  sup {
    background-color: #f3e8ff !important;
    color: #6b21a8 !important;
  }
}

.notificationList {
  max-height: 25rem;
  overflow-y: auto;
}

.notificationItem {
  padding: 1rem;
  border-bottom: 1px solid $dark-blue-15;
  background-color: #ffffff;

  &:last-child {
    border-bottom: none;
  }
}

.unread {
  background-color: #eff6ff80;
}

.notificationContent {
  display: flex;
  align-items: baseline;
  gap: 1rem;
}

.iconCircle {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  flex-shrink: 0;
}

.leadIcon {
  color: $primary-blue;
}

.systemIcon {
  color: $secondary-purple;
}

.taskIcon {
  color: $warning-400-accent-default;
}

.notificationDetails {
  flex: 1;
}

.notificationHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.25rem;
  gap: 0.5rem;
}

.titleContainer {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;

  h4 {
    margin: 0;
    font-weight: 600;
    font-size: 1rem;
    color: $dark-blue-700-text-icon;
  }
}

.priorityBadge {
  padding: 0.125rem 0.5rem;
  border-radius: 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.high {
  background-color: $danger-200-subtle-border;
  color: $danger-700-text-icon;
}

.medium {
  background-color: $warning-200-subtle-border;
  color: $warning-700-text-icon;
}

.low {
  background-color: $success-200-subtle-border;
  color: $success-700-text-icon;
}

.timeAgo {
  font-size: 0.75rem;
  text-align: center;
  color: $dark-blue-400-subtle-icon-border;
}

.description {
  margin: 0.25rem 0;
  font-size: 0.875rem;
  color: $dark-blue-500-secondary-text;
}

.timestamp {
  margin: 0.25rem 0 0;
  font-size: 0.75rem;
  color: $dark-blue-400-subtle-icon-border;
}

.markAsReadButton {
  color: $primary-blue;

  &:hover {
    color: $primary-500-default-link;
  }
}

.deleteButton {
  color: $dark-blue-400-subtle-icon-border;

  &:hover {
    color: $danger-400-accent-hover;
  }
}

.emptyState {
  padding: 1.5rem;
  text-align: center;
  color: $dark-blue-400-subtle-icon-border;
}
