import { Flex, Typo<PERSON>, Divider, Button } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import styles from './queue-layout.module.scss';

const { Title, Paragraph } = Typography;

export const QueueLayout = ({
  icon,
  title,
  label,
  description,
  children,
  button1Icon,
  button1Text,
  button1OnClick,
  classNameButton01,
}) => {
  return (
    <div>
      {title && description ? (
        <div className={`${styles.header_banner} spacing-mb-20`}>
          <Flex gap="small" justify="center" align="center">
            <InfoCircleOutlined className="icon-16 text-primary" />
            <Title className="text-center" level={5}>
              {title}
            </Title>
          </Flex>
          <Paragraph className="text-center">{description}</Paragraph>
        </div>
      ) : null}
      <Box>
        <Flex justify="space-between" align="center">
          <Flex align="center" gap="small">
            {icon && <div className="main-icon-square">{icon && icon}</div>}
            <div>
              <Title style={{ marginBottom: 0 }} level={3}>
                {title}
              </Title>
              {label && <Paragraph>{label}</Paragraph>}
            </div>
          </Flex>
          <Flex justify="flex-start" align="flex-start" gap={8}>
            <Button
              className={classNameButton01 || ''}
              size="large"
              icon={button1Icon}
              type="default"
              onClick={button1OnClick}>
              {button1Text}
            </Button>
          </Flex>
        </Flex>
        <Divider />
        {children}
      </Box>
    </div>
  );
};

const Box = ({ children }) => {
  return (
    <div
      style={{
        borderRadius: '8px',
        padding: '16px',
        backgroundColor: '#fff',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
      }}>
      {children}
    </div>
  );
};
