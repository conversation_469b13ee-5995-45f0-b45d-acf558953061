import { store } from '../store';

export const SET_ASSIGNED_MODE = 'SET_ASSIGNED_MODE';
export const OPEN_SCHOOL_DETAILS = 'OPEN_SCHOOL_DETAILS';
export const CLOSE_SCHOOL_DETAILS = 'CLOSE_SCHOOL_DETAILS';
export const OPEN_TASK_LEAD = 'OPEN_TASK_LEAD';
export const CLOSE_TASK_LEAD = 'CLOSE_TASK_LEAD';
export const TOGGLE_ADVANCED_FILTER = 'TOGGLE_ADVANCED_FILTER';
export const TOGGLE_NEW_LEAD = 'TOGGLE_NEW_LEAD';
export const OPEN_UPDATE_LEAD = 'OPEN_UPDATE_LEAD';
export const CLOSE_UPDATE_LEAD = 'CLOSE_UPDATE_LEAD';

export const SET_STATUS_FILTER = 'SET_STATUS_FILTER';
export const SET_FILTER_STATUS = 'SET_FILTER_STATUS';
export const SET_ADVANCED_FILTERS = 'SET_ADVANCED_FILTERS';
export const SET_SELECTED_LEAD = 'SET_SELECTED_LEAD';
export const CLEAR_SELECTED_LEAD = 'CLEAR_SELECTED_LEAD';
export const SET_SELECTED_LEADS = 'SET_SELECTED_LEADS';
export const SELECT_ALL_LEADS = 'SELECT_ALL_LEADS';
export const CLEAR_SELECTED_LEADS = 'CLEAR_SELECTED_LEADS';

export const SET_USER = 'SET_USER';
export const SET_IN_QUEUE = 'SET_IN_QUEUE';

export const SET_VALUE = 'SET_VALUE';

export const setGlobalValue = (key, value) => store.dispatch({ type: SET_VALUE, payload: { key, value } });

export const setAssignedMode = () => store.dispatch({ type: SET_ASSIGNED_MODE });

export const openSchoolDetails = (schoolId) => store.dispatch({ type: OPEN_SCHOOL_DETAILS, payload: schoolId });

export const closeSchoolDetails = () => store.dispatch({ type: CLOSE_SCHOOL_DETAILS });

export const openTaskLead = (leadId) => store.dispatch({ type: OPEN_TASK_LEAD, payload: leadId });
export const closeTaskLead = () => store.dispatch({ type: CLOSE_TASK_LEAD });

export const openUpdateLead = () => store.dispatch({ type: OPEN_UPDATE_LEAD });
export const closeUpdateLead = () => store.dispatch({ type: CLOSE_UPDATE_LEAD });

export const toggleAdvancedFilter = () => store.dispatch({ type: TOGGLE_ADVANCED_FILTER });

export const toggleUpdateLead = () => store.dispatch({ type: OPEN_UPDATE_LEAD, payload: leadId });

export const toggleNewLead = () => store.dispatch({ type: TOGGLE_NEW_LEAD });

export const setStatusFilter = (status) => store.dispatch({ type: SET_STATUS_FILTER, payload: status });

export const setFilterValue = (value) => store.dispatch({ type: SET_FILTER_STATUS, payload: value });

export const setAdvancedFilters = (filters) => store.dispatch({ type: SET_ADVANCED_FILTERS, payload: filters });

export const setSelectedLead = (value) => store.dispatch({ type: SET_SELECTED_LEAD, payload: value });
export const clearSelectedLead = () => store.dispatch({ type: CLEAR_SELECTED_LEAD });
export const setSelectedLeads = (value) => store.dispatch({ type: SET_SELECTED_LEADS, payload: value });
export const handleSelectAllLeads = (value) => store.dispatch({ type: SELECT_ALL_LEADS, payload: value });

export const clearSelectedLeads = () => store.dispatch({ type: CLEAR_SELECTED_LEADS });

export const setUser = ({ status, tag, role, isSPOTadmin, cloudtalkId }) =>
  store.dispatch({ type: SET_USER, payload: { status, tag, role, isSPOTadmin, cloudtalkId } });
