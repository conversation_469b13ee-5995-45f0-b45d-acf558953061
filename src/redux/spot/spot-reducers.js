import {
  SET_VALUE,
  SET_ASSIGNED_MODE,
  OPEN_SCHOOL_DETAILS,
  CLOSE_SCHOOL_DETAILS,
  OPEN_TASK_LEAD,
  CLOSE_TASK_LEAD,
  TOGGLE_ADVANCED_FILTER,
  TOGGLE_NEW_LEAD,
  SET_STATUS_FILTER,
  SET_FILTER_STATUS,
  SET_ADVANCED_FILTERS,
  SET_SELECTED_LEAD,
  SET_SELECTED_LEADS,
  CLOSE_UPDATE_LEAD,
  OPEN_UPDATE_LEAD,
  SELECT_ALL_LEADS,
  CLEAR_SELECTED_LEADS,
  SET_USER,
  CLEAR_SELECTED_LEAD,
} from './spot-actions';

const initialState = {
  isSPOTadmin: false,
  roles: [],
  userStatus: '',
  cloudtalkId: null,
  queueTag: '',
  isAssignedMode: false,
  isSchoolDetailsOpen: false,
  selectedSchool: null,
  isTaskLeadOpen: false,
  isUpdateLeadOpen: false,
  selectedLead: null,
  isAdvancedFilterOpen: false,
  isNewLeadOpen: false,
  statusFilter: 'new', // Default status filter
  filterValue: '',
  selectedLeads: [],
  messageType: '',
  advancedFilters: {
    fromDate: '',
    toDate: '',
    assignedTo: 'all',
    hasSchools: false,
    leadSources: [],
    grades: [],
    searchNotes: '',
  },
};

export const spotReducer = (state = initialState, action) => {
  switch (action.type) {
    case SET_VALUE:
      return {
        ...state,
        [action.payload.key]: action.payload.value,
      };

    case SET_ASSIGNED_MODE:
      return {
        ...state,
        isAssignedMode: !state.isAssignedMode,
      };

    case OPEN_SCHOOL_DETAILS:
      return {
        ...state,
        isSchoolDetailsOpen: true,
        selectedSchool: action.payload,
      };

    case CLOSE_SCHOOL_DETAILS:
      return {
        ...state,
        isSchoolDetailsOpen: false,
        selectedSchool: null,
      };

    case OPEN_TASK_LEAD:
      return {
        ...state,
        isTaskLeadOpen: true,
      };

    case CLOSE_TASK_LEAD:
      return {
        ...state,
        isTaskLeadOpen: false,
        selectedLead: null,
      };

    case OPEN_UPDATE_LEAD:
      return {
        ...state,
        isUpdateLeadOpen: true,
        // selectedLead: action.payload,
      };

    case CLOSE_UPDATE_LEAD:
      return {
        ...state,
        isUpdateLeadOpen: false,
        // selectedLead: null,
      };

    case TOGGLE_ADVANCED_FILTER:
      return {
        ...state,
        isAdvancedFilterOpen: !state.isAdvancedFilterOpen,
      };

    case TOGGLE_NEW_LEAD:
      return {
        ...state,
        isNewLeadOpen: !state.isNewLeadOpen,
      };

    case SET_STATUS_FILTER:
      return {
        ...state,
        statusFilter: action.payload,
      };

    case SET_FILTER_STATUS:
      return {
        ...state,
        filterValue: action.payload,
      };

    case SET_ADVANCED_FILTERS:
      return {
        ...state,
        advancedFilters: action.payload,
      };

    case SET_SELECTED_LEADS:
      const isSelected = state.selectedLeads.includes(action.payload);

      const currentValue = state.selectedLeads;

      return {
        ...state,
        selectedLeads: isSelected
          ? currentValue.filter((item) => item !== action.payload)
          : [...currentValue, action.payload],
      };

    case SELECT_ALL_LEADS:
      return {
        ...state,
        selectedLeads: action.payload,
      };

    case CLEAR_SELECTED_LEADS:
      return {
        ...state,
        selectedLeads: [],
      };

    case SET_USER:
      return {
        ...state,
        userStatus: action.payload.status,
        queueTag: action.payload.tag,
        roles: action.payload.role,
        isSPOTadmin: action.payload.isSPOTadmin,
        cloudtalkId: action.payload.cloudtalkId,
      };

    case SET_SELECTED_LEAD:
      return {
        ...state,
        selectedLead: action.payload,
      };

    case CLEAR_SELECTED_LEAD:
      return {
        ...state,
        selectedLead: null,
      };

    default:
      return state;
  }
};
